<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Search Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background: #f9f9f9;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .loading {
            color: #007bff;
            font-style: italic;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Web Search Service Test</h1>
        <p>This page tests the improved web search functionality with multiple proxy fallbacks.</p>
        
        <div class="test-section">
            <h3>Quick Test</h3>
            <button onclick="testSearch('artificial intelligence')">Test AI Search</button>
            <button onclick="testSearch('climate change 2024')">Test Climate Search</button>
            <button onclick="testSearch('javascript programming')">Test Programming Search</button>
            <button onclick="clearResults()">Clear Results</button>
            <div id="quick-results"></div>
        </div>

        <div class="test-section">
            <h3>Custom Search</h3>
            <input type="text" id="custom-query" placeholder="Enter your search query..." style="width: 70%; padding: 8px;">
            <button onclick="testCustomSearch()">Search</button>
            <div id="custom-results"></div>
        </div>

        <div class="test-section">
            <h3>Proxy Status</h3>
            <button onclick="testProxies()">Test All Proxies</button>
            <div id="proxy-results"></div>
        </div>
    </div>

    <script type="module">
        // Import the web search service
        import { performWebSearch } from './services/webSearchService.ts';

        window.testSearch = async function(query) {
            const resultsDiv = document.getElementById('quick-results');
            resultsDiv.innerHTML = '<div class="loading">Searching for: ' + query + '...</div>';
            
            try {
                const startTime = Date.now();
                const { results, citations } = await performWebSearch(query);
                const endTime = Date.now();
                
                let html = `<div class="result success">
                    <strong>Search completed in ${endTime - startTime}ms</strong><br>
                    Found ${results.length} results and ${citations.length} citations
                </div>`;
                
                results.slice(0, 5).forEach(result => {
                    html += `<div class="result">
                        <strong>${result.source}:</strong> ${result.title}<br>
                        <small>${result.url}</small><br>
                        ${result.snippet}
                    </div>`;
                });
                
                if (citations.length > 0) {
                    html += '<div class="result"><strong>Citations:</strong><br>';
                    citations.slice(0, 3).forEach(citation => {
                        html += `• ${citation.title} (${citation.source})<br>`;
                    });
                    html += '</div>';
                }
                
                resultsDiv.innerHTML = html;
            } catch (error) {
                resultsDiv.innerHTML = `<div class="result error">
                    <strong>Error:</strong> ${error.message}<br>
                    <pre>${error.stack}</pre>
                </div>`;
            }
        };

        window.testCustomSearch = function() {
            const query = document.getElementById('custom-query').value.trim();
            if (!query) {
                alert('Please enter a search query');
                return;
            }
            
            const resultsDiv = document.getElementById('custom-results');
            resultsDiv.innerHTML = '<div class="loading">Searching...</div>';
            
            window.testSearch(query).then(() => {
                // Move results from quick-results to custom-results
                const quickResults = document.getElementById('quick-results').innerHTML;
                resultsDiv.innerHTML = quickResults;
                document.getElementById('quick-results').innerHTML = '';
            });
        };

        window.testProxies = async function() {
            const resultsDiv = document.getElementById('proxy-results');
            resultsDiv.innerHTML = '<div class="loading">Testing proxy services...</div>';
            
            const testUrl = 'https://httpbin.org/json';
            const proxies = [
                'https://cors-anywhere.herokuapp.com/',
                'https://api.allorigins.win/get?url=',
                'https://api.allorigins.win/raw?url=',
                'https://proxy.cors.sh/',
                'https://r.jina.ai/',
                'https://corsproxy.io/?',
                'https://thingproxy.freeboard.io/fetch/'
            ];
            
            let html = '';
            for (const proxy of proxies) {
                try {
                    const startTime = Date.now();
                    let testUrlFull;
                    if (proxy.includes('allorigins.win/get')) {
                        testUrlFull = proxy + encodeURIComponent(testUrl);
                    } else if (proxy.includes('corsproxy.io')) {
                        testUrlFull = proxy + encodeURIComponent(testUrl);
                    } else {
                        testUrlFull = proxy + testUrl;
                    }
                    
                    const response = await fetch(testUrlFull, { 
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json,text/html,*/*',
                            'User-Agent': 'Mozilla/5.0 (compatible; KResearch/1.0)'
                        }
                    });
                    const endTime = Date.now();
                    
                    if (response.ok) {
                        html += `<div class="result success">
                            ✅ ${proxy} - OK (${endTime - startTime}ms)
                        </div>`;
                    } else {
                        html += `<div class="result error">
                            ❌ ${proxy} - HTTP ${response.status}
                        </div>`;
                    }
                } catch (error) {
                    html += `<div class="result error">
                        ❌ ${proxy} - ${error.message}
                    </div>`;
                }
            }
            
            resultsDiv.innerHTML = html;
        };

        window.clearResults = function() {
            document.getElementById('quick-results').innerHTML = '';
            document.getElementById('custom-results').innerHTML = '';
            document.getElementById('proxy-results').innerHTML = '';
        };

        // Test on page load
        console.log('Web Search Test Page Loaded');
        console.log('Available functions: testSearch, testCustomSearch, testProxies, clearResults');
    </script>
</body>
</html>
