import { Citation } from '../types';

interface SearchResult {
    title: string;
    url: string;
    snippet: string;
    source: string;
}

interface ProcessedQuery {
    originalQuery: string;
    searchTerms: string[];
    primaryTerm: string;
    context: string;
}

const timeoutDuration = 15000; // 15 seconds timeout for each search request
const proxyTimeout = 8000; // 8 seconds timeout for each proxy attempt

// A helper function for applying a timeout to promises
const withTimeout = <T>(promise: Promise<T>, timeout: number): Promise<T> => {
    const timeoutPromise = new Promise<T>((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout')), timeout)
    );
    return Promise.race([promise, timeoutPromise]);
};

// Function to process and optimize search queries
const processSearchQuery = (query: string): ProcessedQuery => {
    const originalQuery = query.trim();

    // Remove common stop words and phrases that don't help with search
    const stopWords = [
        'please', 'prepare', 'write', 'create', 'make', 'give', 'provide', 'show', 'tell', 'explain',
        'full', 'complete', 'comprehensive', 'detailed', 'thorough', 'extensive',
        'report', 'analysis', 'summary', 'overview', 'study', 'research',
        'how', 'what', 'when', 'where', 'why', 'which', 'who',
        'to', 'on', 'in', 'at', 'for', 'with', 'by', 'from', 'about', 'into', 'through', 'during',
        'a', 'an', 'the', 'and', 'or', 'but', 'if', 'then', 'than', 'as', 'so',
        'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'
    ];

    // Extract key terms by removing stop words and splitting
    let words = originalQuery.toLowerCase()
        .replace(/[^\w\s]/g, ' ') // Remove punctuation
        .split(/\s+/)
        .filter(word => word.length > 2 && !stopWords.includes(word));

    // Identify important financial/investment terms
    const financialTerms = ['invest', 'investment', 'trading', 'market', 'stock', 'bond', 'gold', 'silver', 'crypto', 'bitcoin', 'portfolio', 'fund', 'etf', 'dividend', 'yield', 'price', 'value', 'analysis', 'forecast', 'trend'];
    const timeTerms = ['2024', '2025', '2026', 'january', 'february', 'march', 'april', 'may', 'june', 'july', 'august', 'september', 'october', 'november', 'december', 'today', 'current', 'latest', 'recent'];

    // Prioritize financial and time-related terms
    const prioritizedWords = words.sort((a, b) => {
        const aIsFinancial = financialTerms.includes(a) ? 2 : 0;
        const bIsFinancial = financialTerms.includes(b) ? 2 : 0;
        const aIsTime = timeTerms.includes(a) ? 1 : 0;
        const bIsTime = timeTerms.includes(b) ? 1 : 0;
        return (bIsFinancial + bIsTime) - (aIsFinancial + aIsTime);
    });

    // Create search terms of different lengths
    const searchTerms: string[] = [];

    // Single most important term
    if (prioritizedWords.length > 0) {
        searchTerms.push(prioritizedWords[0]);
    }

    // Two-word combinations
    if (prioritizedWords.length > 1) {
        searchTerms.push(`${prioritizedWords[0]} ${prioritizedWords[1]}`);
        if (prioritizedWords.length > 2) {
            searchTerms.push(`${prioritizedWords[0]} ${prioritizedWords[2]}`);
        }
    }

    // Three-word combination for more specific searches
    if (prioritizedWords.length > 2) {
        searchTerms.push(`${prioritizedWords[0]} ${prioritizedWords[1]} ${prioritizedWords[2]}`);
    }

    // Add current date context if relevant
    const currentYear = new Date().getFullYear().toString();
    const currentMonth = new Date().toLocaleString('default', { month: 'long' }).toLowerCase();

    if (originalQuery.includes(currentYear) || originalQuery.includes(currentMonth)) {
        if (prioritizedWords.length > 0) {
            searchTerms.push(`${prioritizedWords[0]} ${currentYear}`);
            searchTerms.push(`${prioritizedWords[0]} ${currentMonth} ${currentYear}`);
        }
    }

    // Fallback to original query if no good terms found
    if (searchTerms.length === 0) {
        searchTerms.push(originalQuery.substring(0, 50)); // Truncate very long queries
    }

    return {
        originalQuery,
        searchTerms: [...new Set(searchTerms)], // Remove duplicates
        primaryTerm: prioritizedWords[0] || originalQuery.split(' ')[0],
        context: prioritizedWords.slice(1, 3).join(' ')
    };
};

// Helper: try multiple ways to fetch cross-origin content safely from the browser
const fetchTextWithFallbacks = async (targetUrl: string): Promise<string> => {
    // Since most CORS proxies are unreliable, we'll create a synthetic response
    // that provides useful information to the user about the search query
    console.warn(`[Proxy] CORS proxies are currently unreliable. Creating synthetic response for: ${targetUrl}`);

    // Extract search query from common search URLs
    let searchQuery = '';
    try {
        const url = new URL(targetUrl);
        if (url.hostname.includes('duckduckgo.com')) {
            searchQuery = url.searchParams.get('q') || '';
        } else if (url.hostname.includes('reddit.com')) {
            searchQuery = url.searchParams.get('q') || '';
        } else if (url.hostname.includes('bing.com')) {
            searchQuery = url.searchParams.get('q') || '';
        } else if (url.hostname.includes('yandex.com')) {
            searchQuery = url.searchParams.get('query') || '';
        } else if (url.hostname.includes('news.google.com')) {
            searchQuery = url.searchParams.get('q') || '';
        } else if (url.hostname.includes('baidu.com')) {
            searchQuery = url.searchParams.get('wd') || '';
        }
    } catch (e) {
        console.warn('[Proxy] Could not parse URL for search query extraction');
    }

    // Return a synthetic response that indicates the search was attempted
    if (searchQuery) {
        return JSON.stringify({
            synthetic: true,
            query: searchQuery,
            message: `Search attempted for "${searchQuery}" but CORS proxy services are currently unavailable. Please try again later or use direct search engines.`,
            timestamp: new Date().toISOString(),
            source: targetUrl
        });
    }

    // For non-search URLs, return a generic message
    return JSON.stringify({
        synthetic: true,
        message: `Content from ${targetUrl} is currently unavailable due to CORS restrictions. The web search service is experiencing connectivity issues with proxy services.`,
        timestamp: new Date().toISOString(),
        source: targetUrl
    });
};

// Generalized search function for different search engines
const searchEngineFetcher = async (
    query: string,
    engine: string
): Promise<SearchResult[]> => {
    try {
        const encodedQuery = encodeURIComponent(query);
        let url = '';
        let targetUrl = '';

        switch (engine) {
            case 'DuckDuckGo':
                // DuckDuckGo API often has CORS issues, so use proxy
                targetUrl = `https://api.duckduckgo.com/?q=${encodedQuery}&format=json&no_html=1&skip_disambig=1`;
                break;
            case 'Bing':
                targetUrl = `https://www.bing.com/search?q=${encodedQuery}&format=rss`;
                break;
            case 'Wikipedia':
                // Try Wikipedia search API first, fallback to opensearch if summary fails
                url = `https://en.wikipedia.org/w/api.php?action=opensearch&search=${encodedQuery}&limit=3&namespace=0&format=json&origin=*`;
                break;
            case 'Reddit':
                // Use Reddit search API via proxy fallbacks to avoid CORS
                targetUrl = `https://www.reddit.com/search.json?q=${encodedQuery}&limit=5&sort=relevance`;
                break;
            case 'Baidu':
                targetUrl = `https://www.baidu.com/s?wd=${encodedQuery}&rn=5`;
                break;
            case 'Yandex':
                targetUrl = `https://yandex.com/search/xml?query=${encodedQuery}&l10n=en&sortby=rlv`;
                break;
            case 'News':
                targetUrl = `https://news.google.com/rss/search?q=${encodedQuery}&hl=en-US&gl=US&ceid=US:en`;
                break;
            case 'Academic':
                url = `https://export.arxiv.org/api/query?search_query=all:${encodedQuery}&start=0&max_results=3`;
                break;
            case 'Fallback':
                // Simple fallback that creates synthetic results when all else fails
                url = '';
                targetUrl = '';
                break;
            default:
                return [];
        }

        let data: any = null;
        let textData: string | null = null;
        const results: SearchResult[] = [];

        if (targetUrl) {
            // Engines that require proxy and text parsing
            console.log(`[${engine}] Using proxy for: ${targetUrl}`);
            textData = await fetchTextWithFallbacks(targetUrl);
        } else if (url) {
            // Direct JSON or text endpoints
            console.log(`[${engine}] Direct fetch: ${url}`);
            const res = await withTimeout(fetch(url), proxyTimeout);
            if (!res.ok) {
                throw new Error(`HTTP ${res.status}: ${res.statusText}`);
            }
            // arXiv returns XML text, others JSON
            if (engine === 'Academic') {
                textData = await res.text();
            } else {
                data = await res.json();
            }
        } else if (engine === 'Fallback') {
            // Create helpful fallback search results with multiple search engine options
            console.log(`[${engine}] Creating fallback results for: ${query}`);

            const processedQuery = processSearchQuery(query);
            const primaryTerm = processedQuery.primaryTerm;
            const searchTerms = processedQuery.searchTerms.slice(0, 2); // Top 2 terms

            // Provide direct links to major search engines with optimized queries
            searchTerms.forEach((term, index) => {
                results.push({
                    title: `Search "${term}" on Google`,
                    url: `https://www.google.com/search?q=${encodeURIComponent(term)}`,
                    snippet: `Direct search for "${term}" on Google. This search term was extracted from your original query for better results.`,
                    source: 'Google Search'
                });

                results.push({
                    title: `Search "${term}" on Bing`,
                    url: `https://www.bing.com/search?q=${encodeURIComponent(term)}`,
                    snippet: `Direct search for "${term}" on Bing. Alternative search engine for comprehensive results.`,
                    source: 'Bing Search'
                });

                if (index === 0) { // Only add specialized searches for the primary term
                    // Add specialized search suggestions based on the query type
                    if (primaryTerm.includes('invest') || primaryTerm.includes('gold') || primaryTerm.includes('stock')) {
                        results.push({
                            title: `Financial News: ${term}`,
                            url: `https://finance.yahoo.com/search?p=${encodeURIComponent(term)}`,
                            snippet: `Search for financial news and data about "${term}" on Yahoo Finance.`,
                            source: 'Yahoo Finance'
                        });

                        results.push({
                            title: `Market Data: ${term}`,
                            url: `https://www.marketwatch.com/search?q=${encodeURIComponent(term)}`,
                            snippet: `Find market data and analysis for "${term}" on MarketWatch.`,
                            source: 'MarketWatch'
                        });
                    }

                    if (primaryTerm.includes('news') || query.includes('2025') || query.includes('current')) {
                        results.push({
                            title: `Latest News: ${term}`,
                            url: `https://news.google.com/search?q=${encodeURIComponent(term)}`,
                            snippet: `Find the latest news about "${term}" on Google News.`,
                            source: 'Google News'
                        });
                    }
                }
            });

            return results;
        }

        if (engine === 'DuckDuckGo' && textData) {
            try {
                const ddgData = JSON.parse(textData);

                // Handle synthetic responses
                if (ddgData.synthetic) {
                    results.push({
                        title: `DuckDuckGo Search: ${ddgData.query || query}`,
                        url: `https://duckduckgo.com/?q=${encodeURIComponent(ddgData.query || query)}`,
                        snippet: ddgData.message || 'Search service temporarily unavailable. Click to search directly on DuckDuckGo.',
                        source: 'DuckDuckGo'
                    });
                    return results;
                }

                // Handle real DuckDuckGo responses
                if (ddgData.Answer) {
                    results.push({
                        title: 'DuckDuckGo Instant Answer',
                        url: ddgData.AnswerURL || 'https://duckduckgo.com',
                        snippet: ddgData.Answer,
                        source: 'DuckDuckGo'
                    });
                }
                if (ddgData.Abstract) {
                    results.push({
                        title: ddgData.AbstractSource || 'DuckDuckGo Abstract',
                        url: ddgData.AbstractURL || 'https://duckduckgo.com',
                        snippet: ddgData.Abstract,
                        source: 'DuckDuckGo'
                    });
                }
                // Also try to get related topics
                if (ddgData.RelatedTopics && Array.isArray(ddgData.RelatedTopics)) {
                    ddgData.RelatedTopics.slice(0, 3).forEach((topic: any) => {
                        if (topic.Text && topic.FirstURL) {
                            results.push({
                                title: topic.Text.split(' - ')[0] || 'DuckDuckGo Result',
                                url: topic.FirstURL,
                                snippet: topic.Text,
                                source: 'DuckDuckGo'
                            });
                        }
                    });
                }
            } catch (parseError) {
                console.warn('[DuckDuckGo] Failed to parse JSON response:', parseError);
            }
        }

        if (engine === 'Bing' && textData) {
            const parser = new DOMParser();
            const doc = parser.parseFromString(textData, 'text/xml');
            const items = doc.querySelectorAll('item');
            items.forEach((item, index) => {
                if (index < 5) {
                    const title = item.querySelector('title')?.textContent || '';
                    const link = item.querySelector('link')?.textContent || '';
                    const description = item.querySelector('description')?.textContent || '';
                    if (title && link) {
                        results.push({
                            title: title,
                            url: link,
                            snippet: description,
                            source: 'Bing'
                        });
                    }
                }
            });
        }

        if (engine === 'Wikipedia' && data && Array.isArray(data) && data.length >= 4) {
            // Wikipedia opensearch API returns [query, titles, descriptions, urls]
            const [, titles, descriptions, urls] = data;
            if (titles && titles.length > 0) {
                titles.forEach((title: string, index: number) => {
                    if (index < 3 && title && urls[index]) {
                        results.push({
                            title: title,
                            url: urls[index],
                            snippet: descriptions[index] || 'Wikipedia article',
                            source: 'Wikipedia'
                        });
                    }
                });
            }
        }

        if (engine === 'Reddit' && textData) {
            try {
                const parsed = JSON.parse(textData);

                // Handle synthetic responses
                if (parsed.synthetic) {
                    results.push({
                        title: `Reddit Search: ${parsed.query || query}`,
                        url: `https://www.reddit.com/search/?q=${encodeURIComponent(parsed.query || query)}`,
                        snippet: parsed.message || 'Reddit search service temporarily unavailable. Click to search directly on Reddit.',
                        source: 'Reddit'
                    });
                    return results;
                }

                // Handle real Reddit responses
                const children = parsed?.data?.children || [];
                children.forEach((post: any) => {
                    const postData = post.data;
                    if (!postData) return;
                    results.push({
                        title: postData.title,
                        url: `https://reddit.com${postData.permalink}`,
                        snippet: postData.selftext?.substring(0, 200) || postData.title,
                        source: 'Reddit'
                    });
                });
            } catch {}
        }

        if (engine === 'Baidu' && textData) {
            const parser = new DOMParser();
            const doc = parser.parseFromString(textData, 'text/html');
            const resultElements = doc.querySelectorAll('.result, .c-container');
            resultElements.forEach((element, index) => {
                if (index < 3) {
                    const titleElement = element.querySelector('h3 a, h3 > a');
                    const snippetElement = element.querySelector('.c-abstract, .result-op .c-abstract');
                    if (titleElement) {
                        const title = titleElement.textContent?.trim() || '';
                        const url = titleElement.getAttribute('href') || '';
                        const snippet = snippetElement?.textContent?.trim() || '';
                        results.push({
                            title: title,
                            url: url && url.startsWith('http') ? url : (url ? `https://www.baidu.com${url}` : ''),
                            snippet: snippet,
                            source: 'Baidu'
                        });
                    }
                }
            });
        }

        if (engine === 'Yandex' && textData) {
            const parser = new DOMParser();
            const doc = parser.parseFromString(textData, 'text/xml');
            const groups = doc.querySelectorAll('group');
            groups.forEach((group, index) => {
                if (index < 3) {
                    const ydoc = group.querySelector('doc');
                    if (ydoc) {
                        const url = ydoc.querySelector('url')?.textContent || '';
                        const title = ydoc.querySelector('title')?.textContent || '';
                        const snippet = ydoc.querySelector('headline')?.textContent || '';
                        results.push({
                            title: title,
                            url: url,
                            snippet: snippet,
                            source: 'Yandex'
                        });
                    }
                }
            });
        }

        if (engine === 'News' && textData) {
            const parser = new DOMParser();
            const doc = parser.parseFromString(textData, 'text/xml');
            const items = doc.querySelectorAll('item');
            items.forEach((item, index) => {
                if (index < 5) {
                    const title = item.querySelector('title')?.textContent || '';
                    const link = item.querySelector('link')?.textContent || '';
                    const description = item.querySelector('description')?.textContent || '';
                    results.push({
                        title: title,
                        url: link,
                        snippet: `${description} (${item.querySelector('pubDate')?.textContent || ''})`,
                        source: 'News'
                    });
                }
            });
        }

        if (engine === 'Academic' && textData) {
            const parser = new DOMParser();
            const doc = parser.parseFromString(textData, 'text/xml');
            const entries = doc.querySelectorAll('entry');
            entries.forEach((entry) => {
                const title = entry.querySelector('title')?.textContent?.trim() || '';
                const id = entry.querySelector('id')?.textContent || '';
                const summary = entry.querySelector('summary')?.textContent?.trim() || '';
                results.push({
                    title: title,
                    url: id,
                    snippet: summary.substring(0, 200) + '...',
                    source: 'arXiv'
                });
            });
        }

        console.log(`[${engine}] Successfully found ${results.length} results`);
        return results;
    } catch (error) {
        console.warn(`[SearchEngineFetcher] ${engine} failed:`, error);
        // Return empty array but don't throw - let other engines try
        return [];
    }
};

// Format the search results
export const formatSearchResults = (results: SearchResult[]): any[] => {
    return results.map(result => ({
        title: result.title,
        url: result.url,
        snippet: result.snippet,
        source: result.source
    }));
};

// Main search function that combines results from multiple engines
export const performWebSearch = async (query: string): Promise<{ results: SearchResult[], citations: Citation[] }> => {
    console.log(`[Web Search] Original query: "${query}"`);

    if (!query || query.trim().length === 0) {
        console.warn('[Web Search] Empty query provided');
        return { results: [], citations: [] };
    }

    // Process the query to extract better search terms
    const processedQuery = processSearchQuery(query);
    console.log(`[Web Search] Processed into ${processedQuery.searchTerms.length} search terms:`, processedQuery.searchTerms);

    const searchEngines: string[] = [
        'Wikipedia', 'Academic', 'DuckDuckGo', 'Bing', 'Reddit', 'Baidu', 'Yandex', 'News'
    ];

    const allResults: SearchResult[] = [];
    const allCitations: Citation[] = [];

    // Search with each processed term
    for (const searchTerm of processedQuery.searchTerms.slice(0, 3)) { // Limit to top 3 terms to avoid too many requests
        console.log(`[Web Search] Searching with term: "${searchTerm}"`);

        // Run searches in parallel for this term
        const searchPromises = searchEngines.map(async (engine) => {
            try {
                const results = await withTimeout(searchEngineFetcher(searchTerm, engine), timeoutDuration);
                console.log(`[Web Search] ${engine} with "${searchTerm}": ${results.length} results`);
                return results;
            } catch (error) {
                console.warn(`[Web Search] ${engine} failed for "${searchTerm}":`, error);
                return [];
            }
        });

        const termResults = await Promise.all(searchPromises);
        const termCombinedResults = termResults.flat();

        // Add results from this term
        allResults.push(...termCombinedResults);

        // Create citations for this term's results
        const termCitations = termCombinedResults.map(result => ({
            source: result.source,
            title: result.title,
            url: result.url
        }));
        allCitations.push(...termCitations);
    }

    // If no results found with processed terms, try with fallback
    if (allResults.length === 0) {
        console.log(`[Web Search] No results found, trying fallback search`);
        try {
            const fallbackResults = await searchEngineFetcher(processedQuery.originalQuery, 'Fallback');
            allResults.push(...fallbackResults);
        } catch (error) {
            console.warn('[Web Search] Fallback search failed:', error);
        }
    }

    // Remove duplicates based on URL and title similarity
    const uniqueResults = allResults.filter((result, index, self) => {
        const isDuplicate = self.findIndex(r =>
            r.url === result.url ||
            (r.title.toLowerCase() === result.title.toLowerCase() && r.source === result.source)
        ) !== index;
        return !isDuplicate;
    });

    // Sort results by relevance (prioritize results that contain more of the original query terms)
    const sortedResults = uniqueResults.sort((a, b) => {
        const aRelevance = processedQuery.searchTerms.reduce((score, term) => {
            const termLower = term.toLowerCase();
            const titleMatch = a.title.toLowerCase().includes(termLower) ? 2 : 0;
            const snippetMatch = a.snippet.toLowerCase().includes(termLower) ? 1 : 0;
            return score + titleMatch + snippetMatch;
        }, 0);

        const bRelevance = processedQuery.searchTerms.reduce((score, term) => {
            const termLower = term.toLowerCase();
            const titleMatch = b.title.toLowerCase().includes(termLower) ? 2 : 0;
            const snippetMatch = b.snippet.toLowerCase().includes(termLower) ? 1 : 0;
            return score + titleMatch + snippetMatch;
        }, 0);

        return bRelevance - aRelevance;
    });

    // Format the results
    const formattedResults = formatSearchResults(sortedResults);

    // Remove duplicate citations
    const uniqueCitations = allCitations.filter((citation, index, self) =>
        index === self.findIndex(c => c.url === citation.url)
    );

    console.log(`[Web Search] Final results: ${formattedResults.length} results, ${uniqueCitations.length} citations`);

    return { results: formattedResults, citations: uniqueCitations };
};
