import { Citation } from '../types';

interface SearchResult {
    title: string;
    url: string;
    snippet: string;
    source: string;
}

const timeoutDuration = 15000; // 15 seconds timeout for each search request
const proxyTimeout = 8000; // 8 seconds timeout for each proxy attempt

// A helper function for applying a timeout to promises
const withTimeout = <T>(promise: Promise<T>, timeout: number): Promise<T> => {
    const timeoutPromise = new Promise<T>((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout')), timeout)
    );
    return Promise.race([promise, timeoutPromise]);
};

// Helper: try multiple ways to fetch cross-origin content safely from the browser
const fetchTextWithFallbacks = async (targetUrl: string): Promise<string> => {
    // Since most CORS proxies are unreliable, we'll create a synthetic response
    // that provides useful information to the user about the search query
    console.warn(`[Proxy] CORS proxies are currently unreliable. Creating synthetic response for: ${targetUrl}`);

    // Extract search query from common search URLs
    let searchQuery = '';
    try {
        const url = new URL(targetUrl);
        if (url.hostname.includes('duckduckgo.com')) {
            searchQuery = url.searchParams.get('q') || '';
        } else if (url.hostname.includes('reddit.com')) {
            searchQuery = url.searchParams.get('q') || '';
        } else if (url.hostname.includes('bing.com')) {
            searchQuery = url.searchParams.get('q') || '';
        } else if (url.hostname.includes('yandex.com')) {
            searchQuery = url.searchParams.get('query') || '';
        } else if (url.hostname.includes('news.google.com')) {
            searchQuery = url.searchParams.get('q') || '';
        } else if (url.hostname.includes('baidu.com')) {
            searchQuery = url.searchParams.get('wd') || '';
        }
    } catch (e) {
        console.warn('[Proxy] Could not parse URL for search query extraction');
    }

    // Return a synthetic response that indicates the search was attempted
    if (searchQuery) {
        return JSON.stringify({
            synthetic: true,
            query: searchQuery,
            message: `Search attempted for "${searchQuery}" but CORS proxy services are currently unavailable. Please try again later or use direct search engines.`,
            timestamp: new Date().toISOString(),
            source: targetUrl
        });
    }

    // For non-search URLs, return a generic message
    return JSON.stringify({
        synthetic: true,
        message: `Content from ${targetUrl} is currently unavailable due to CORS restrictions. The web search service is experiencing connectivity issues with proxy services.`,
        timestamp: new Date().toISOString(),
        source: targetUrl
    });
};

// Generalized search function for different search engines
const searchEngineFetcher = async (
    query: string,
    engine: string
): Promise<SearchResult[]> => {
    try {
        const encodedQuery = encodeURIComponent(query);
        let url = '';
        let targetUrl = '';

        switch (engine) {
            case 'DuckDuckGo':
                // DuckDuckGo API often has CORS issues, so use proxy
                targetUrl = `https://api.duckduckgo.com/?q=${encodedQuery}&format=json&no_html=1&skip_disambig=1`;
                break;
            case 'Bing':
                targetUrl = `https://www.bing.com/search?q=${encodedQuery}&format=rss`;
                break;
            case 'Wikipedia':
                // Try Wikipedia search API first, fallback to opensearch if summary fails
                url = `https://en.wikipedia.org/w/api.php?action=opensearch&search=${encodedQuery}&limit=3&namespace=0&format=json&origin=*`;
                break;
            case 'Reddit':
                // Use Reddit search API via proxy fallbacks to avoid CORS
                targetUrl = `https://www.reddit.com/search.json?q=${encodedQuery}&limit=5&sort=relevance`;
                break;
            case 'Baidu':
                targetUrl = `https://www.baidu.com/s?wd=${encodedQuery}&rn=5`;
                break;
            case 'Yandex':
                targetUrl = `https://yandex.com/search/xml?query=${encodedQuery}&l10n=en&sortby=rlv`;
                break;
            case 'News':
                targetUrl = `https://news.google.com/rss/search?q=${encodedQuery}&hl=en-US&gl=US&ceid=US:en`;
                break;
            case 'Academic':
                url = `https://export.arxiv.org/api/query?search_query=all:${encodedQuery}&start=0&max_results=3`;
                break;
            case 'Fallback':
                // Simple fallback that creates synthetic results when all else fails
                url = '';
                targetUrl = '';
                break;
            default:
                return [];
        }

        let data: any = null;
        let textData: string | null = null;
        const results: SearchResult[] = [];

        if (targetUrl) {
            // Engines that require proxy and text parsing
            console.log(`[${engine}] Using proxy for: ${targetUrl}`);
            textData = await fetchTextWithFallbacks(targetUrl);
        } else if (url) {
            // Direct JSON or text endpoints
            console.log(`[${engine}] Direct fetch: ${url}`);
            const res = await withTimeout(fetch(url), proxyTimeout);
            if (!res.ok) {
                throw new Error(`HTTP ${res.status}: ${res.statusText}`);
            }
            // arXiv returns XML text, others JSON
            if (engine === 'Academic') {
                textData = await res.text();
            } else {
                data = await res.json();
            }
        } else if (engine === 'Fallback') {
            // Create synthetic search results as a last resort
            console.log(`[${engine}] Creating fallback results for: ${query}`);
            results.push({
                title: `Search results for "${query}"`,
                url: `https://www.google.com/search?q=${encodedQuery}`,
                snippet: `Please search manually for "${query}" using your preferred search engine. The automated search services are currently experiencing connectivity issues.`,
                source: 'Fallback'
            });
            return results;
        }

        if (engine === 'DuckDuckGo' && textData) {
            try {
                const ddgData = JSON.parse(textData);

                // Handle synthetic responses
                if (ddgData.synthetic) {
                    results.push({
                        title: `DuckDuckGo Search: ${ddgData.query || query}`,
                        url: `https://duckduckgo.com/?q=${encodeURIComponent(ddgData.query || query)}`,
                        snippet: ddgData.message || 'Search service temporarily unavailable. Click to search directly on DuckDuckGo.',
                        source: 'DuckDuckGo'
                    });
                    return results;
                }

                // Handle real DuckDuckGo responses
                if (ddgData.Answer) {
                    results.push({
                        title: 'DuckDuckGo Instant Answer',
                        url: ddgData.AnswerURL || 'https://duckduckgo.com',
                        snippet: ddgData.Answer,
                        source: 'DuckDuckGo'
                    });
                }
                if (ddgData.Abstract) {
                    results.push({
                        title: ddgData.AbstractSource || 'DuckDuckGo Abstract',
                        url: ddgData.AbstractURL || 'https://duckduckgo.com',
                        snippet: ddgData.Abstract,
                        source: 'DuckDuckGo'
                    });
                }
                // Also try to get related topics
                if (ddgData.RelatedTopics && Array.isArray(ddgData.RelatedTopics)) {
                    ddgData.RelatedTopics.slice(0, 3).forEach((topic: any) => {
                        if (topic.Text && topic.FirstURL) {
                            results.push({
                                title: topic.Text.split(' - ')[0] || 'DuckDuckGo Result',
                                url: topic.FirstURL,
                                snippet: topic.Text,
                                source: 'DuckDuckGo'
                            });
                        }
                    });
                }
            } catch (parseError) {
                console.warn('[DuckDuckGo] Failed to parse JSON response:', parseError);
            }
        }

        if (engine === 'Bing' && textData) {
            const parser = new DOMParser();
            const doc = parser.parseFromString(textData, 'text/xml');
            const items = doc.querySelectorAll('item');
            items.forEach((item, index) => {
                if (index < 5) {
                    const title = item.querySelector('title')?.textContent || '';
                    const link = item.querySelector('link')?.textContent || '';
                    const description = item.querySelector('description')?.textContent || '';
                    if (title && link) {
                        results.push({
                            title: title,
                            url: link,
                            snippet: description,
                            source: 'Bing'
                        });
                    }
                }
            });
        }

        if (engine === 'Wikipedia' && data && Array.isArray(data) && data.length >= 4) {
            // Wikipedia opensearch API returns [query, titles, descriptions, urls]
            const [, titles, descriptions, urls] = data;
            if (titles && titles.length > 0) {
                titles.forEach((title: string, index: number) => {
                    if (index < 3 && title && urls[index]) {
                        results.push({
                            title: title,
                            url: urls[index],
                            snippet: descriptions[index] || 'Wikipedia article',
                            source: 'Wikipedia'
                        });
                    }
                });
            }
        }

        if (engine === 'Reddit' && textData) {
            try {
                const parsed = JSON.parse(textData);

                // Handle synthetic responses
                if (parsed.synthetic) {
                    results.push({
                        title: `Reddit Search: ${parsed.query || query}`,
                        url: `https://www.reddit.com/search/?q=${encodeURIComponent(parsed.query || query)}`,
                        snippet: parsed.message || 'Reddit search service temporarily unavailable. Click to search directly on Reddit.',
                        source: 'Reddit'
                    });
                    return results;
                }

                // Handle real Reddit responses
                const children = parsed?.data?.children || [];
                children.forEach((post: any) => {
                    const postData = post.data;
                    if (!postData) return;
                    results.push({
                        title: postData.title,
                        url: `https://reddit.com${postData.permalink}`,
                        snippet: postData.selftext?.substring(0, 200) || postData.title,
                        source: 'Reddit'
                    });
                });
            } catch {}
        }

        if (engine === 'Baidu' && textData) {
            const parser = new DOMParser();
            const doc = parser.parseFromString(textData, 'text/html');
            const resultElements = doc.querySelectorAll('.result, .c-container');
            resultElements.forEach((element, index) => {
                if (index < 3) {
                    const titleElement = element.querySelector('h3 a, h3 > a');
                    const snippetElement = element.querySelector('.c-abstract, .result-op .c-abstract');
                    if (titleElement) {
                        const title = titleElement.textContent?.trim() || '';
                        const url = titleElement.getAttribute('href') || '';
                        const snippet = snippetElement?.textContent?.trim() || '';
                        results.push({
                            title: title,
                            url: url && url.startsWith('http') ? url : (url ? `https://www.baidu.com${url}` : ''),
                            snippet: snippet,
                            source: 'Baidu'
                        });
                    }
                }
            });
        }

        if (engine === 'Yandex' && textData) {
            const parser = new DOMParser();
            const doc = parser.parseFromString(textData, 'text/xml');
            const groups = doc.querySelectorAll('group');
            groups.forEach((group, index) => {
                if (index < 3) {
                    const ydoc = group.querySelector('doc');
                    if (ydoc) {
                        const url = ydoc.querySelector('url')?.textContent || '';
                        const title = ydoc.querySelector('title')?.textContent || '';
                        const snippet = ydoc.querySelector('headline')?.textContent || '';
                        results.push({
                            title: title,
                            url: url,
                            snippet: snippet,
                            source: 'Yandex'
                        });
                    }
                }
            });
        }

        if (engine === 'News' && textData) {
            const parser = new DOMParser();
            const doc = parser.parseFromString(textData, 'text/xml');
            const items = doc.querySelectorAll('item');
            items.forEach((item, index) => {
                if (index < 5) {
                    const title = item.querySelector('title')?.textContent || '';
                    const link = item.querySelector('link')?.textContent || '';
                    const description = item.querySelector('description')?.textContent || '';
                    results.push({
                        title: title,
                        url: link,
                        snippet: `${description} (${item.querySelector('pubDate')?.textContent || ''})`,
                        source: 'News'
                    });
                }
            });
        }

        if (engine === 'Academic' && textData) {
            const parser = new DOMParser();
            const doc = parser.parseFromString(textData, 'text/xml');
            const entries = doc.querySelectorAll('entry');
            entries.forEach((entry) => {
                const title = entry.querySelector('title')?.textContent?.trim() || '';
                const id = entry.querySelector('id')?.textContent || '';
                const summary = entry.querySelector('summary')?.textContent?.trim() || '';
                results.push({
                    title: title,
                    url: id,
                    snippet: summary.substring(0, 200) + '...',
                    source: 'arXiv'
                });
            });
        }

        console.log(`[${engine}] Successfully found ${results.length} results`);
        return results;
    } catch (error) {
        console.warn(`[SearchEngineFetcher] ${engine} failed:`, error);
        // Return empty array but don't throw - let other engines try
        return [];
    }
};

// Format the search results
export const formatSearchResults = (results: SearchResult[]): any[] => {
    return results.map(result => ({
        title: result.title,
        url: result.url,
        snippet: result.snippet,
        source: result.source
    }));
};

// Main search function that combines results from multiple engines
export const performWebSearch = async (query: string): Promise<{ results: SearchResult[], citations: Citation[] }> => {
    console.log(`[Web Search] Searching for: "${query}"`);

    if (!query || query.trim().length === 0) {
        console.warn('[Web Search] Empty query provided');
        return { results: [], citations: [] };
    }

    const searchEngines: string[] = [
        'Wikipedia', 'Academic', 'DuckDuckGo', 'Bing', 'Reddit', 'Baidu', 'Yandex', 'News', 'Fallback'
    ];

    // Run searches in parallel with timeouts
    const searchPromises = searchEngines.map(async (engine) => {
        try {
            const results = await withTimeout(searchEngineFetcher(query.trim(), engine), timeoutDuration);
            console.log(`[Web Search] ${engine}: ${results.length} results`);
            return results;
        } catch (error) {
            console.warn(`[Web Search] ${engine} failed:`, error);
            return [];
        }
    });

    const allResults = await Promise.all(searchPromises);
    const combinedResults = allResults.flat();

    // Remove duplicates based on URL
    const uniqueResults = combinedResults.filter((result, index, self) => 
        index === self.findIndex(r => r.url === result.url)
    );

    // Format the results
    const formattedResults = formatSearchResults(uniqueResults);

    // Assuming citations are needed from the unique results
    const citations = uniqueResults.map(result => ({
        source: result.source,
        title: result.title,
        url: result.url
    }));

    return { results: formattedResults, citations };
};
