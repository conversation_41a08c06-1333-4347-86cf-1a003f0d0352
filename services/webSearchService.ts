import { Citation } from '../types';

interface SearchResult {
    title: string;
    url: string;
    snippet: string;
    source: string;
}

const timeoutDuration = 10000; // 10 seconds timeout for each search request

// A helper function for applying a timeout to promises
const withTimeout = <T>(promise: Promise<T>, timeout: number): Promise<T> => {
    const timeoutPromise = new Promise<T>((_, reject) => 
        setTimeout(() => reject(new Error('Search timeout')), timeout)
    );
    return Promise.race([promise, timeoutPromise]);
};

// Helper: try multiple ways to fetch cross-origin content safely from the browser
const fetchTextWithFallbacks = async (targetUrl: string): Promise<string> => {
    // 1) Try AllOrigins JSON wrapper (/get)
    try {
        const r1 = await fetch(`https://api.allorigins.win/get?url=${encodeURIComponent(targetUrl)}`);
        if (r1.ok) {
            const j = await r1.json();
            if (j && typeof j.contents === 'string') return j.contents as string;
        }
    } catch {}

    // 2) Try AllOrigins raw (returns text directly)
    try {
        const r2 = await fetch(`https://api.allorigins.win/raw?url=${encodeURIComponent(targetUrl)}`);
        if (r2.ok) return await r2.text();
    } catch {}

    // 3) Try r.jina.ai proxy (returns fetched content with permissive CORS)
    try {
        // r.jina.ai supports both http and https targets
        const prefixed = targetUrl.startsWith('http') ? targetUrl : `https://${targetUrl}`;
        const r3 = await fetch(`https://r.jina.ai/http/${prefixed.replace(/^https?:\/\//, '')}`);
        if (r3.ok) return await r3.text();
    } catch {}

    throw new Error('All proxy fetch attempts failed');
};

// Generalized search function for different search engines
const searchEngineFetcher = async (
    query: string,
    engine: string
): Promise<SearchResult[]> => {
    try {
        const encodedQuery = encodeURIComponent(query);
        let url = '';
        let targetUrl = '';

        switch (engine) {
            case 'DuckDuckGo':
                url = `https://api.duckduckgo.com/?q=${encodedQuery}&format=json&no_html=1&skip_disambig=1`;
                break;
            case 'Bing':
                targetUrl = `https://www.bing.com/search?q=${encodedQuery}&format=rss`;
                break;
            case 'Wikipedia':
                url = `https://en.wikipedia.org/api/rest_v1/page/summary/${encodedQuery}`;
                break;
            case 'Reddit':
                // Use Reddit search API via proxy fallbacks to avoid CORS
                targetUrl = `https://www.reddit.com/search.json?q=${encodedQuery}&limit=5&sort=relevance`;
                break;
            case 'Baidu':
                targetUrl = `https://www.baidu.com/s?wd=${encodedQuery}&rn=5`;
                break;
            case 'Yandex':
                targetUrl = `https://yandex.com/search/xml?query=${encodedQuery}&l10n=en&sortby=rlv`;
                break;
            case 'News':
                targetUrl = `https://news.google.com/rss/search?q=${encodedQuery}&hl=en-US&gl=US&ceid=US:en`;
                break;
            case 'Academic':
                url = `https://export.arxiv.org/api/query?search_query=all:${encodedQuery}&start=0&max_results=3`;
                break;
            default:
                return [];
        }

        let data: any = null;
        let textData: string | null = null;

        if (targetUrl) {
            // Engines that require proxy and text parsing
            textData = await fetchTextWithFallbacks(targetUrl);
        } else if (url) {
            // Direct JSON or text endpoints
            const res = await fetch(url);
            // arXiv returns XML text, others JSON
            if (engine === 'Academic') {
                textData = await res.text();
            } else {
                data = await res.json();
            }
        }

        const results: SearchResult[] = [];

        if (engine === 'DuckDuckGo' && data.Answer) {
            results.push({
                title: 'DuckDuckGo Instant Answer',
                url: data.AnswerURL || 'https://duckduckgo.com',
                snippet: data.Answer,
                source: 'DuckDuckGo'
            });
        }

        if (engine === 'DuckDuckGo' && data.Abstract) {
            results.push({
                title: data.AbstractSource || 'DuckDuckGo Abstract',
                url: data.AbstractURL || 'https://duckduckgo.com',
                snippet: data.Abstract,
                source: 'DuckDuckGo'
            });
        }

        if (engine === 'Bing' && textData) {
            const parser = new DOMParser();
            const doc = parser.parseFromString(textData, 'text/xml');
            const items = doc.querySelectorAll('item');
            items.forEach((item, index) => {
                if (index < 5) {
                    const title = item.querySelector('title')?.textContent || '';
                    const link = item.querySelector('link')?.textContent || '';
                    const description = item.querySelector('description')?.textContent || '';
                    if (title && link) {
                        results.push({
                            title: title,
                            url: link,
                            snippet: description,
                            source: 'Bing'
                        });
                    }
                }
            });
        }

        if (engine === 'Wikipedia' && data) {
            results.push({
                title: data.title || 'Wikipedia',
                url: data.content_urls?.desktop?.page || `https://en.wikipedia.org/wiki/${encodedQuery}`,
                snippet: data.extract || 'Wikipedia article',
                source: 'Wikipedia'
            });
        }

        if (engine === 'Reddit' && textData) {
            try {
                const parsed = JSON.parse(textData);
                const children = parsed?.data?.children || [];
                children.forEach((post: any) => {
                    const postData = post.data;
                    if (!postData) return;
                    results.push({
                        title: postData.title,
                        url: `https://reddit.com${postData.permalink}`,
                        snippet: postData.selftext?.substring(0, 200) || postData.title,
                        source: 'Reddit'
                    });
                });
            } catch {}
        }

        if (engine === 'Baidu' && textData) {
            const parser = new DOMParser();
            const doc = parser.parseFromString(textData, 'text/html');
            const resultElements = doc.querySelectorAll('.result, .c-container');
            resultElements.forEach((element, index) => {
                if (index < 3) {
                    const titleElement = element.querySelector('h3 a, h3 > a');
                    const snippetElement = element.querySelector('.c-abstract, .result-op .c-abstract');
                    if (titleElement) {
                        const title = titleElement.textContent?.trim() || '';
                        const url = titleElement.getAttribute('href') || '';
                        const snippet = snippetElement?.textContent?.trim() || '';
                        results.push({
                            title: title,
                            url: url && url.startsWith('http') ? url : (url ? `https://www.baidu.com${url}` : ''),
                            snippet: snippet,
                            source: 'Baidu'
                        });
                    }
                }
            });
        }

        if (engine === 'Yandex' && textData) {
            const parser = new DOMParser();
            const doc = parser.parseFromString(textData, 'text/xml');
            const groups = doc.querySelectorAll('group');
            groups.forEach((group, index) => {
                if (index < 3) {
                    const ydoc = group.querySelector('doc');
                    if (ydoc) {
                        const url = ydoc.querySelector('url')?.textContent || '';
                        const title = ydoc.querySelector('title')?.textContent || '';
                        const snippet = ydoc.querySelector('headline')?.textContent || '';
                        results.push({
                            title: title,
                            url: url,
                            snippet: snippet,
                            source: 'Yandex'
                        });
                    }
                }
            });
        }

        if (engine === 'News' && textData) {
            const parser = new DOMParser();
            const doc = parser.parseFromString(textData, 'text/xml');
            const items = doc.querySelectorAll('item');
            items.forEach((item, index) => {
                if (index < 5) {
                    const title = item.querySelector('title')?.textContent || '';
                    const link = item.querySelector('link')?.textContent || '';
                    const description = item.querySelector('description')?.textContent || '';
                    results.push({
                        title: title,
                        url: link,
                        snippet: `${description} (${item.querySelector('pubDate')?.textContent || ''})`,
                        source: 'News'
                    });
                }
            });
        }

        if (engine === 'Academic' && textData) {
            const parser = new DOMParser();
            const doc = parser.parseFromString(textData, 'text/xml');
            const entries = doc.querySelectorAll('entry');
            entries.forEach((entry) => {
                const title = entry.querySelector('title')?.textContent?.trim() || '';
                const id = entry.querySelector('id')?.textContent || '';
                const summary = entry.querySelector('summary')?.textContent?.trim() || '';
                results.push({
                    title: title,
                    url: id,
                    snippet: summary.substring(0, 200) + '...',
                    source: 'arXiv'
                });
            });
        }

        return results;
    } catch (error) {
        console.warn(`[SearchEngineFetcher] ${engine} failed:`, error);
        return [];
    }
};

// Format the search results
export const formatSearchResults = (results: SearchResult[]): any[] => {
    return results.map(result => ({
        title: result.title,
        url: result.url,
        snippet: result.snippet,
        source: result.source
    }));
};

// Main search function that combines results from multiple engines
export const performWebSearch = async (query: string): Promise<{ results: SearchResult[], citations: Citation[] }> => {
    console.log(`[Web Search] Searching for: "${query}"`);

    if (!query || query.trim().length === 0) {
        console.warn('[Web Search] Empty query provided');
        return { results: [], citations: [] };
    }

    const searchEngines: string[] = [
        'DuckDuckGo', 'Bing', 'Wikipedia', 'Reddit', 'Baidu', 'Yandex', 'News', 'Academic'
    ];

    // Run searches in parallel with timeouts
    const searchPromises = searchEngines.map(async (engine) => {
        try {
            const results = await withTimeout(searchEngineFetcher(query.trim(), engine), timeoutDuration);
            console.log(`[Web Search] ${engine}: ${results.length} results`);
            return results;
        } catch (error) {
            console.warn(`[Web Search] ${engine} failed:`, error);
            return [];
        }
    });

    const allResults = await Promise.all(searchPromises);
    const combinedResults = allResults.flat();

    // Remove duplicates based on URL
    const uniqueResults = combinedResults.filter((result, index, self) => 
        index === self.findIndex(r => r.url === result.url)
    );

    // Format the results
    const formattedResults = formatSearchResults(uniqueResults);

    // Assuming citations are needed from the unique results
    const citations = uniqueResults.map(result => ({
        source: result.source,
        title: result.title,
        url: result.url
    }));

    return { results: formattedResults, citations };
};
